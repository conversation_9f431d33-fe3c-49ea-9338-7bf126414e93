#!/usr/bin/env node

/**
 * Update all @2925.com accounts to use password "William1!"
 */

import { db } from './src/lib/db.js'
import bcrypt from 'bcryptjs'

async function updatePasswordsFor2925Accounts() {
  try {
    console.log('🔧 Updating passwords for all @2925.com accounts...')
    
    // The password to set for all @2925.com accounts
    const newPassword = 'William1!'
    const passwordHash = await bcrypt.hash(newPassword, 10)
    
    console.log('📝 New password:', newPassword)
    console.log('🔑 New hash:', passwordHash)
    console.log()

    // Find all users with @2925.com email addresses
    const users2925 = await db.user.findMany({
      where: {
        account: {
          endsWith: '@2925.com'
        }
      },
      select: {
        id: true,
        account: true,
        name: true
      }
    })

    console.log(`Found ${users2925.length} @2925.com accounts:`)
    users2925.forEach(user => {
      console.log(`  - ${user.account} (${user.name})`)
    })
    console.log()

    if (users2925.length === 0) {
      console.log('❌ No @2925.com accounts found!')
      return
    }

    // Update all @2925.com accounts with the new password hash
    const updateResult = await db.user.updateMany({
      where: {
        account: {
          endsWith: '@2925.com'
        }
      },
      data: {
        passwordHash: passwordHash
      }
    })

    console.log(`✅ Successfully updated ${updateResult.count} accounts`)
    console.log()
    console.log('🎉 All @2925.com accounts now have the password: "William1!"')
    console.log()
    console.log('📋 You can now login with:')
    users2925.forEach(user => {
      console.log(`   Email: ${user.account}`)
      console.log(`   Password: William1!`)
      console.log()
    })

  } catch (error) {
    console.error('❌ Error updating passwords:', error)
  } finally {
    await db.$disconnect()
  }
}

// Run the update
updatePasswordsFor2925Accounts()
