import { NextResponse } from 'next/server'
import { db } from '@/lib/db'
import bcrypt from 'bcryptjs'

export async function POST(request) {
  try {
    console.log('🔧 Updating passwords for all @2925.com accounts...')
    
    // The password to set for all @2925.com accounts
    const newPassword = 'William1!'
    const passwordHash = await bcrypt.hash(newPassword, 10)
    
    console.log('📝 New password:', newPassword)
    console.log('🔑 New hash:', passwordHash)

    // Find all users with @2925.com email addresses
    const users2925 = await db.user.findMany({
      where: {
        account: {
          endsWith: '@2925.com'
        }
      },
      select: {
        id: true,
        account: true,
        name: true
      }
    })

    console.log(`Found ${users2925.length} @2925.com accounts:`)
    users2925.forEach(user => {
      console.log(`  - ${user.account} (${user.name})`)
    })

    if (users2925.length === 0) {
      return NextResponse.json({ 
        success: false, 
        message: 'No @2925.com accounts found!' 
      })
    }

    // Update all @2925.com accounts with the new password hash
    const updateResult = await db.user.updateMany({
      where: {
        account: {
          endsWith: '@2925.com'
        }
      },
      data: {
        passwordHash: passwordHash
      }
    })

    console.log(`✅ Successfully updated ${updateResult.count} accounts`)

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${updateResult.count} @2925.com accounts`,
      password: newPassword,
      accounts: users2925.map(user => user.account),
      updatedCount: updateResult.count
    })

  } catch (error) {
    console.error('❌ Error updating passwords:', error)
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 })
  }
}
