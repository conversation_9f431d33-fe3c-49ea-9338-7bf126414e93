#!/usr/bin/env node

/**
 * Reset password for specific users for testing
 */

import { db } from './src/lib/db.js'
import bcrypt from 'bcryptjs'

async function resetPassword() {
  try {
    // Set a simple password for testing
    const testPassword = 'test123456'
    const passwordHash = await bcrypt.hash(testPassword, 10)

    console.log('🔧 Resetting passwords for testing...')
    console.log('New password:', testPassword)
    console.log('New hash:', passwordHash)

    // Update specific users
    const usersToUpdate = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]

    for (const email of usersToUpdate) {
      const result = await db.user.update({
        where: { account: email },
        data: { passwordHash },
      })
      
      console.log(`✅ Updated password for: ${email}`)
    }

    console.log(`\n🎉 Password reset complete!`)
    console.log(`📝 Test password: "${testPassword}"`)

  } catch (error) {
    console.error('❌ Error resetting passwords:', error)
  } finally {
    await db.$disconnect()
  }
}

resetPassword()
