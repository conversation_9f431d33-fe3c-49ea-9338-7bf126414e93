#!/usr/bin/env node

/**
 * Debug script to check database content and user authentication
 * Run with: node debug-database.js
 */

import { db } from './src/lib/db.js'

async function debugDatabase() {
  console.log('🔍 Debugging database content...\n')

  try {
    // Check all tables for record counts and sample data
    console.log('📊 Table Record Counts:')
    console.log('=' .repeat(50))

    // Count records in each table
    const [userCount, templateCount, faceSourceCount, generatedMediaCount] = await Promise.all([
      db.user.count(),
      db.targetTemplate.count(),
      db.faceSource.count(),
      db.generatedMedia.count(),
    ])

    console.log(`Users: ${userCount}`)
    console.log(`Target Templates: ${templateCount}`)
    console.log(`Face Sources: ${faceSourceCount}`)
    console.log(`Generated Media: ${generatedMediaCount}`)

    console.log('\n📋 Sample Data:')
    console.log('=' .repeat(50))

    // Get sample users
    const users = await db.user.findMany({
      take: 5,
      select: {
        id: true,
        account: true,
        name: true,
        lastLogin: true,
      },
    })
    console.log('\n👥 Users:')
    users.forEach(user => {
      console.log(`  - ID: ${user.id}`)
      console.log(`    Account: ${user.account}`)
      console.log(`    Name: ${user.name || 'N/A'}`)
      console.log(`    Last Login: ${user.lastLogin || 'Never'}`)
      console.log()
    })

    // Get sample templates
    const templates = await db.targetTemplate.findMany({
      take: 5,
      select: {
        id: true,
        filename: true,
        type: true,
        isActive: true,
        authorId: true,
        createdAt: true,
      },
    })
    console.log('\n🎬 Target Templates:')
    templates.forEach(template => {
      console.log(`  - ID: ${template.id}`)
      console.log(`    Filename: ${template.filename}`)
      console.log(`    Type: ${template.type}`)
      console.log(`    Active: ${template.isActive}`)
      console.log(`    Author ID: ${template.authorId || 'N/A'}`)
      console.log(`    Created: ${template.createdAt}`)
      console.log()
    })

    // Get sample face sources
    const faceSources = await db.faceSource.findMany({
      take: 5,
      select: {
        id: true,
        filename: true,
        isActive: true,
        authorId: true,
        createdAt: true,
      },
    })
    console.log('\n👤 Face Sources:')
    if (faceSources.length === 0) {
      console.log('  ❌ No face sources found!')
    } else {
      faceSources.forEach(source => {
        console.log(`  - ID: ${source.id}`)
        console.log(`    Filename: ${source.filename}`)
        console.log(`    Active: ${source.isActive}`)
        console.log(`    Author ID: ${source.authorId || 'N/A'}`)
        console.log(`    Created: ${source.createdAt}`)
        console.log()
      })
    }

    // Get sample generated media
    const generatedMedia = await db.generatedMedia.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        type: true,
        isActive: true,
        authorId: true,
        createdAt: true,
      },
    })
    console.log('\n🎥 Generated Media:')
    if (generatedMedia.length === 0) {
      console.log('  ❌ No generated media found!')
    } else {
      generatedMedia.forEach(media => {
        console.log(`  - ID: ${media.id}`)
        console.log(`    Name: ${media.name}`)
        console.log(`    Type: ${media.type}`)
        console.log(`    Active: ${media.isActive}`)
        console.log(`    Author ID: ${media.authorId || 'N/A'}`)
        console.log(`    Created: ${media.createdAt}`)
        console.log()
      })
    }

    // Check for orphaned records (records without valid authorId)
    console.log('\n🔍 Orphaned Records Analysis:')
    console.log('=' .repeat(50))

    const orphanedFaceSources = await db.faceSource.count({
      where: { authorId: null }
    })
    const orphanedGeneratedMedia = await db.generatedMedia.count({
      where: { authorId: null }
    })

    console.log(`Face Sources without author: ${orphanedFaceSources}`)
    console.log(`Generated Media without author: ${orphanedGeneratedMedia}`)

    if (orphanedFaceSources > 0 || orphanedGeneratedMedia > 0) {
      console.log('\n⚠️  Found orphaned records! These won\'t show up for authenticated users.')
      console.log('   Consider assigning them to a user or updating the API to show orphaned records.')
    }

  } catch (error) {
    console.error('❌ Error debugging database:', error)
  } finally {
    await db.$disconnect()
  }
}

// Run the debug function
debugDatabase()
