#!/usr/bin/env node

/**
 * Quick fix script to set passwords for OAuth users
 * This allows them to login with email/password
 */

import { db } from './src/lib/db.js'
import bcrypt from 'bcryptjs'

async function fixUserPasswords() {
  console.log('🔧 Setting passwords for OAuth users...\n')

  try {
    // Default password for demo (you can change this)
    const defaultPassword = 'William1!'
    const passwordHash = await bcrypt.hash(defaultPassword, 10)

    // Find users without password hash (OAuth users)
    const usersWithoutPassword = await db.user.findMany({
      where: {
        passwordHash: null,
      },
      select: {
        id: true,
        account: true,
        name: true,
      },
    })

    console.log(`Found ${usersWithoutPassword.length} users without passwords`)

    if (usersWithoutPassword.length === 0) {
      console.log('✅ All users already have passwords!')
      return
    }

    // Update each user with the default password
    for (const user of usersWithoutPassword) {
      await db.user.update({
        where: { id: user.id },
        data: { passwordHash },
      })
      
      console.log(`✅ Set password for: ${user.account}`)
    }

    console.log(`\n🎉 Successfully set passwords for ${usersWithoutPassword.length} users`)
    console.log(`📝 Default password: "${defaultPassword}"`)
    console.log('\n💡 Users can now login with:')
    usersWithoutPassword.forEach(user => {
      console.log(`   Email: ${user.account}`)
      console.log(`   Password: ${defaultPassword}`)
      console.log()
    })

  } catch (error) {
    console.error('❌ Error fixing user passwords:', error)
  } finally {
    await db.$disconnect()
  }
}

// Run the fix
fixUserPasswords()
